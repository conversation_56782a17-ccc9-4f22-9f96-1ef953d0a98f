import Image from "next/image";
import Link from "next/link";
import { X, Home as HomeIcon, BookOpen, Coffee, Settings, Database } from "lucide-react";

interface NavigationSidebarProps {
  isOpen: boolean;
  isClosing: boolean;
  onClose: () => void;
  sidebarRef: React.RefObject<HTMLDivElement>;
}

export function NavigationSidebar({ isOpen, isClosing, onClose, sidebarRef }: NavigationSidebarProps) {
  if (!isOpen) return null;

  return (
    <>
      {/* Overlay */}
      <div
        className={`fixed inset-0 bg-black/50 z-40 ${isClosing ? 'animate-fade-out' : 'animate-fade-in'}`}
        onClick={onClose}
        aria-hidden="true"
      />

      {/* Sidebar content */}
      <div
        ref={sidebarRef}
        className={`fixed top-0 left-0 h-full w-80 bg-card border-r border-border shadow-lg z-50 ${isClosing ? 'animate-slide-out-left' : 'animate-slide-in-left'}`}
        role="dialog"
        aria-modal="true"
        aria-label="Menu de navegação"
      >
        {/* Sidebar header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div className="flex items-center gap-3">
            <Image
              src="/cereja.png"
              alt="Logo Cereja - Calculadora do Método 4:6"
              width={40}
              height={40}
              className="object-contain"
              quality={75}
            />
            <div>
              <h2 className="text-xl font-bold text-foreground">cereja</h2>
              <p className="text-sm text-muted-foreground">sua calculadora pessoal ♥</p>
            </div>
          </div>
          <button
            onClick={onClose}
            aria-label="Fechar menu"
            className="p-2 hover:bg-accent rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
          >
            <X size={20} aria-hidden="true" />
          </button>
        </div>

        {/* Navigation menu */}
        <nav className="p-6" role="navigation" aria-label="Menu principal">
          <div className="space-y-2">
            <Link
              href="/"
              onClick={onClose}
              aria-current="page"
              className="flex items-center gap-3 px-4 py-3 text-primary bg-primary/10 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            >
              <HomeIcon size={20} aria-hidden="true" />
              <span className="font-medium">Calculadora 4:6</span>
            </Link>

            <Link
              href="/glossario"
              onClick={onClose}
              className="flex items-center gap-3 px-4 py-3 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            >
              <BookOpen size={20} aria-hidden="true" />
              <span className="font-medium">Glossário</span>
            </Link>

            {/* Links desabilitados - Em Construção */}
            <div
              title="Em Construção"
              className="flex items-center gap-3 px-4 py-3 text-muted-foreground/50 rounded-lg opacity-50 cursor-not-allowed pointer-events-none"
              aria-disabled="true"
            >
              <Settings size={20} aria-hidden="true" />
              <span className="font-medium">
                Método 4:6 <span className="text-xs">(Em Construção)</span>
              </span>
            </div>

            <div
              title="Em Construção"
              className="flex items-center gap-3 px-4 py-3 text-muted-foreground/50 rounded-lg opacity-50 cursor-not-allowed pointer-events-none"
              aria-disabled="true"
            >
              <Database size={20} aria-hidden="true" />
              <span className="font-medium">
                Base de Conhecimento <span className="text-xs">(Em Construção)</span>
              </span>
            </div>
          </div>

          {/* Botão de doação */}
          <div className="mt-6 pt-6 border-t border-border">
            <a
              href="https://ko-fi.com/pedrogott"
              target="_blank"
              rel="noopener noreferrer"
              onClick={onClose}
              className="w-full flex items-center gap-3 px-4 py-3 text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl border border-orange-400/30 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
            >
              <Coffee size={20} aria-hidden="true" />
              <div className="flex flex-col text-left">
                <span className="font-medium text-sm">Gostou do projeto?</span>
                <span className="text-xs opacity-80">Pague-me um café!</span>
              </div>
            </a>
          </div>

          {/* Link de Política de Privacidade - discreto */}
          <div className="mt-4 pt-4 border-t border-border/50">
            <Link
              href="/privacidade"
              onClick={onClose}
              className="block px-4 py-2 text-xs text-muted-foreground hover:text-muted-foreground/80 transition-colors text-center focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded"
            >
              Política de Privacidade
            </Link>
          </div>
        </nav>
      </div>
    </>
  );
}
