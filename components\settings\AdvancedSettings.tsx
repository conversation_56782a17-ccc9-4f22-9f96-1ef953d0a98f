import { useAdvancedPreferences } from '@/hooks/useAdvancedPreferences';

interface AdvancedSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

export function AdvancedSettings({ isOpen, onClose }: AdvancedSettingsProps) {
  const { preferences, updatePreference, resetToDefaults } = useAdvancedPreferences();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-card border border-border rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-y-auto">
        <div className="flex items-center justify-between p-4 border-b border-border">
          <h3 className="text-lg font-semibold text-card-foreground">
            Configurações Avançadas
          </h3>
          <button
            onClick={onClose}
            className="p-1 rounded-md hover:bg-accent transition-colors"
            aria-label="<PERSON><PERSON><PERSON> configurações"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
          </button>
        </div>

        <div className="p-4 space-y-6">
          {/* Áudio */}
          <div>
            <h4 className="font-medium text-foreground mb-3">Áudio</h4>
            <div className="space-y-3">
              <label className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Sons habilitados</span>
                <input
                  type="checkbox"
                  checked={preferences.audioEnabled}
                  onChange={(e) => updatePreference('audioEnabled', e.target.checked)}
                  className="rounded"
                />
              </label>
              
              {preferences.audioEnabled && (
                <label className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Volume</span>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={preferences.audioVolume}
                    onChange={(e) => updatePreference('audioVolume', parseFloat(e.target.value))}
                    className="w-20"
                  />
                </label>
              )}
            </div>
          </div>

          {/* Interface */}
          <div>
            <h4 className="font-medium text-foreground mb-3">Interface</h4>
            <div className="space-y-3">
              <label className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Animações</span>
                <input
                  type="checkbox"
                  checked={preferences.animationsEnabled}
                  onChange={(e) => updatePreference('animationsEnabled', e.target.checked)}
                  className="rounded"
                />
              </label>
              
              <label className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Scroll automático</span>
                <input
                  type="checkbox"
                  checked={preferences.autoScrollEnabled}
                  onChange={(e) => updatePreference('autoScrollEnabled', e.target.checked)}
                  className="rounded"
                />
              </label>
              
              <label className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Modo compacto</span>
                <input
                  type="checkbox"
                  checked={preferences.compactMode}
                  onChange={(e) => updatePreference('compactMode', e.target.checked)}
                  className="rounded"
                />
              </label>
            </div>
          </div>

          {/* Performance */}
          <div>
            <h4 className="font-medium text-foreground mb-3">Performance</h4>
            <div className="space-y-3">
              <label className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Cache habilitado</span>
                <input
                  type="checkbox"
                  checked={preferences.cacheEnabled}
                  onChange={(e) => updatePreference('cacheEnabled', e.target.checked)}
                  className="rounded"
                />
              </label>
              
              <label className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Modo debug</span>
                <input
                  type="checkbox"
                  checked={preferences.debugMode}
                  onChange={(e) => updatePreference('debugMode', e.target.checked)}
                  className="rounded"
                />
              </label>
            </div>
          </div>

          {/* Acessibilidade */}
          <div>
            <h4 className="font-medium text-foreground mb-3">Acessibilidade</h4>
            <div className="space-y-3">
              <label className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Alto contraste</span>
                <input
                  type="checkbox"
                  checked={preferences.highContrastMode}
                  onChange={(e) => updatePreference('highContrastMode', e.target.checked)}
                  className="rounded"
                />
              </label>
              
              <label className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Movimento reduzido</span>
                <input
                  type="checkbox"
                  checked={preferences.reducedMotion}
                  onChange={(e) => updatePreference('reducedMotion', e.target.checked)}
                  className="rounded"
                />
              </label>
              
              <label className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Otimizado para leitores de tela</span>
                <input
                  type="checkbox"
                  checked={preferences.screenReaderOptimized}
                  onChange={(e) => updatePreference('screenReaderOptimized', e.target.checked)}
                  className="rounded"
                />
              </label>
            </div>
          </div>

          {/* Botões de ação */}
          <div className="flex gap-2 pt-4 border-t border-border">
            <button
              onClick={resetToDefaults}
              className="flex-1 px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-accent transition-colors"
            >
              Restaurar Padrões
            </button>
            <button
              onClick={onClose}
              className="flex-1 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            >
              Salvar
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
