import { useCallback } from 'react';

export function useKeyboardNavigation() {
  // Função para selecionar todo o texto ao focar
  const handleFocus = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
    e.target.select();
  }, []);

  // Função para desabilitar scroll e setas nos inputs numéricos
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "ArrowUp" || e.key === "ArrowDown") {
      e.preventDefault();
    }
    
    // Navegação por Tab melhorada
    if (e.key === "Tab") {
      // Permitir navegação normal por Tab
      return;
    }
    
    // Enter para confirmar valor
    if (e.key === "Enter") {
      e.currentTarget.blur();
    }
  }, []);

  // Função para desabilitar scroll do mouse em inputs numéricos
  const handleWheel = useCallback((e: React.WheelEvent<HTMLInputElement>) => {
    e.currentTarget.blur();
  }, []);

  // Função para navegação por teclado em botões
  const handleButtonKeyDown = useCallback((e: React.KeyboardEvent<HTMLButtonElement>, onClick: () => void) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      onClick();
    }
  }, []);

  return {
    handleFocus,
    handleKeyDown,
    handleWheel,
    handleButtonKeyDown
  };
}
