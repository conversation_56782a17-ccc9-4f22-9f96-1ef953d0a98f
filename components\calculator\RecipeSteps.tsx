import { forwardRef } from 'react';
import { RecipeStep } from '@/types/calculator';

interface RecipeStepsProps {
  tempo: number;
  contandoRegressivo: boolean;
  tempoTotal: number;
  receita: RecipeStep[];
  obterPassoAtual: () => number;
}

export const RecipeSteps = forwardRef<HTMLDivElement, RecipeStepsProps>(
  ({ tempo, contandoRegressivo, tempoTotal, receita, obterPassoAtual }, ref) => {
    const receitaTerminada = tempo >= tempoTotal && !contandoRegressivo;
    const passoAtual = obterPassoAtual();

    return (
      <div
        className={`mt-4 sm:mt-6 transition-all duration-700 ease-in-out ${
          receitaTerminada
            ? "opacity-0 max-h-0 overflow-hidden transform scale-95"
            : "opacity-100 max-h-[1000px] transform scale-100"
        }`}
        ref={ref}
      >
        <div className="mb-3 sm:mb-4">
          <h3 className="text-xs sm:text-sm font-medium text-foreground">
            Receita
          </h3>
        </div>
        <div className="space-y-1.5 sm:space-y-2" role="list" aria-label="Passos da receita">
          {receita.map((passo, index) => {
            const isAtivo = index === passoAtual && !receitaTerminada;
            const isConcluido = index < passoAtual || (index === passoAtual && receitaTerminada);
            const isProximo = index === passoAtual + 1 && !receitaTerminada;

            return (
              <div
                key={index}
                role="listitem"
                aria-current={isAtivo ? 'step' : undefined}
                className={`p-2.5 sm:p-3 rounded-md text-xs sm:text-sm transition-all duration-500 ease-in-out border-2 transform ${
                  isAtivo
                    ? "bg-primary/50 border-primary shadow-md scale-105"
                    : isConcluido
                    ? "bg-muted opacity-60 border-transparent scale-98"
                    : isProximo
                    ? "bg-accent/10 border-accent/25 scale-102"
                    : "bg-secondary/25 border-transparent scale-100"
                }`}
              >
                <span className="text-foreground">
                  <span className={`inline-flex items-center justify-center w-5 h-5 sm:w-6 sm:h-6 rounded-full text-xs font-bold mr-2 transition-all duration-500 ease-in-out transform ${
                    isAtivo
                      ? "bg-primary text-primary-foreground animate-pulse scale-110"
                      : isConcluido
                      ? "bg-muted-foreground text-background scale-90"
                      : "bg-primary text-primary-foreground scale-100"
                  }`}>
                    {index + 1}
                  </span>
                  Aos <span className="font-bold">{passo.tempo}</span>, despeje <span className="font-bold">{passo.quantidade}ml</span> de água.{index > 0 && ` (Total ${passo.total}ml)`}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    );
  }
);

RecipeSteps.displayName = 'RecipeSteps';
