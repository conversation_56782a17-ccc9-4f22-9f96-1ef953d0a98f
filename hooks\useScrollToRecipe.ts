import { useCallback } from 'react';

export function useScrollToRecipe() {
  const scrollToRecipe = useCallback((receitaRef: React.RefObject<HTMLDivElement | null>) => {
    if (receitaRef.current && window.innerWidth < 768) {
      setTimeout(() => {
        receitaRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }, 100);
    }
  }, []);

  return { scrollToRecipe };
}
