// Tipos para a calculadora 4:6

export interface RecipeStep {
  tempo: string;
  tempoSegundos: number;
  quantidade: number;
  total: number;
}

export interface CalculatorState {
  cafe: string;
  agua: string;
  proporcao: string;
  perfilSabor: string;
  perfilCorpo: string;
  aguaBloqueada: boolean;
  proporcaoBloqueada: boolean;
}

export interface TimerState {
  tempo: number;
  rodando: boolean;
  contandoRegressivo: boolean;
  contagemRegressiva: number;
}

export interface UIState {
  modalAberto: boolean;
  sidebarAberta: boolean;
  sidebarFechando: boolean;
}

export interface UserPreferences {
  cafe: string;
  proporcao: string;
  perfilSabor: string;
  perfilCorpo: string;
  aguaBloqueada: boolean;
  proporcaoBloqueada: boolean;
}

export type PerfilSabor = "Mais Acidez" | "Equilibrado" | "Mais Doçura";
export type PerfilCorpo = "Menos Corpo" | "Equilibrado" | "Mais Corpo";

export interface ProfileOption {
  nome: PerfilSabor | PerfilCorpo;
  icone: string;
  alt: string;
}
