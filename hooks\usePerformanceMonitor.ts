import { useEffect, useCallback } from 'react';

interface PerformanceMetrics {
  renderTime: number;
  componentName: string;
  timestamp: number;
}

export function usePerformanceMonitor(componentName: string) {
  const startTime = performance.now();

  useEffect(() => {
    const endTime = performance.now();
    const renderTime = endTime - startTime;

    // Log apenas em desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      console.log(`🚀 ${componentName} rendered in ${renderTime.toFixed(2)}ms`);
    }

    // Enviar métricas para analytics em produção (se necessário)
    if (process.env.NODE_ENV === 'production' && renderTime > 100) {
      // Componente demorou mais que 100ms para renderizar
      console.warn(`⚠️ Slow render detected: ${componentName} took ${renderTime.toFixed(2)}ms`);
    }
  }, [componentName, startTime]);

  const measureFunction = useCallback((functionName: string, fn: () => void) => {
    const start = performance.now();
    fn();
    const end = performance.now();
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`⚡ ${functionName} executed in ${(end - start).toFixed(2)}ms`);
    }
  }, []);

  return { measureFunction };
}
