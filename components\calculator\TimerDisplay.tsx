interface TimerDisplayProps {
  tempo: number;
  contagemRegressiva: number;
  contandoRegressivo: boolean;
  rodando: boolean;
  tempoTotal: number;
  formatarTempo: (segundos: number) => string;
  receitaFinalizada: boolean;
}

export function TimerDisplay({
  tempo,
  contagemRegressiva,
  contandoRegressivo,
  rodando,
  tempoTotal,
  formatarTempo,
  receitaFinalizada
}: TimerDisplayProps) {
  return (
    <div className="flex flex-col items-center mb-1 sm:mb-2">
      <div className={`text-2xl sm:text-3xl font-bold px-3 sm:px-4 py-2 sm:py-3 rounded-lg border tabular-nums tracking-wider mb-2 transition-all duration-500 ${
        tempo >= tempoTotal && !contandoRegressivo
          ? "text-primary-foreground bg-primary border-primary shadow-lg animate-pulse"
          : tempo > 0 && !rodando && !contandoRegressivo
          ? "text-foreground bg-secondary border-border animate-pulse"
          : "text-foreground bg-secondary border-border"
      }`}>
        {contandoRegressivo ? contagemRegressiva : formatarTempo(tempo)}
      </div>

      {receitaFinalizada && (
        <div className="text-center space-y-2 animate-in fade-in duration-700">
          <div className="text-lg sm:text-xl font-semibold text-primary">
            ✨ Receita Finalizada! ✨
          </div>
          <div className="text-sm sm:text-base text-muted-foreground">
            Aguarde a extração terminar e aproveite seu café! ☕
          </div>
        </div>
      )}
    </div>
  );
}
