import { useState, useCallback, useRef, useEffect } from 'react';
import { UIState } from '@/types/calculator';

export function useUI() {
  const [state, setState] = useState<UIState>({
    modalAberto: false,
    sidebarAberta: false,
    sidebarFechando: false
  });

  const sidebarRef = useRef<HTMLDivElement | null>(null);

  // Função para fechar sidebar com animação
  const fecharSidebar = useCallback(() => {
    setState(prev => ({ ...prev, sidebarFechando: true }));
    setTimeout(() => {
      setState(prev => ({
        ...prev,
        sidebarAberta: false,
        sidebarFechando: false
      }));
    }, 300);
  }, []);

  // Fechar sidebar ao clicar fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (sidebarRef.current && !sidebarRef.current.contains(event.target as Node)) {
        fecharSidebar();
      }
    };

    if (state.sidebarAberta) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [state.sidebarAberta, fecharSidebar]);

  const abrirSidebar = useCallback(() => {
    setState(prev => ({ ...prev, sidebarAberta: true }));
  }, []);

  const abrirModal = useCallback(() => {
    setState(prev => ({ ...prev, modalAberto: true }));
  }, []);

  const fecharModal = useCallback(() => {
    setState(prev => ({ ...prev, modalAberto: false }));
  }, []);

  return {
    state,
    sidebarRef,
    abrirSidebar,
    fecharSidebar,
    abrirModal,
    fecharModal
  };
}
