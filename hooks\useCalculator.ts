import { useState, useCallback, useEffect } from 'react';
import { CalculatorState, RecipeStep, PerfilSabor, PerfilCorpo } from '@/types/calculator';
import { DEFAULT_VALUES, RECIPE_TIMING } from '@/constants/app';
import { useLocalStorage } from './useLocalStorage';
import { useOptimizedCalculations } from './useOptimizedCalculations';
import { useDebounce } from './useDebounce';
import { usePerformanceMonitor } from './usePerformanceMonitor';
import { validateCafeAmount, validateAguaAmount, validateProporcao } from '@/lib/validation';

export function useCalculator() {
  const { measureFunction } = usePerformanceMonitor('useCalculator');

  const [state, setState] = useState<CalculatorState>({
    cafe: DEFAULT_VALUES.cafe,
    agua: DEFAULT_VALUES.agua,
    proporcao: DEFAULT_VALUES.proporcao,
    perfilSabor: DEFAULT_VALUES.perfilSabor,
    perfilCorpo: DEFAULT_VALUES.perfilCorpo,
    aguaBloqueada: DEFAULT_VALUES.aguaBloqueada,
    proporcaoBloqueada: DEFAULT_VALUES.proporcaoBloqueada
  });

  // Debounce dos valores para otimizar cálculos
  const debouncedAgua = useDebounce(state.agua, 300);
  const debouncedPerfilSabor = useDebounce(state.perfilSabor, 300);
  const debouncedPerfilCorpo = useDebounce(state.perfilCorpo, 300);

  const { salvarPreferencias, carregarPreferencias } = useLocalStorage();
  const { receita: calcularReceita } = useOptimizedCalculations({
    agua: debouncedAgua,
    perfilSabor: debouncedPerfilSabor,
    perfilCorpo: debouncedPerfilCorpo
  });

  // Função para arredondar valores
  const arredondar = useCallback((valor: number): string => {
    return Math.round(valor).toString();
  }, []);

  // Carregar preferências ao inicializar
  useEffect(() => {
    const preferencias = carregarPreferencias();
    if (preferencias) {
      setState(prev => ({
        ...prev,
        ...preferencias
      }));

      // Recalcular água com os valores carregados
      const numCafe = parseFloat(preferencias.cafe);
      const numProporcao = parseFloat(preferencias.proporcao);
      if (numCafe > 0 && numProporcao > 0) {
        setState(prev => ({
          ...prev,
          agua: arredondar(numCafe * numProporcao)
        }));
      }
    }
  }, [carregarPreferencias, arredondar]);

  // Salvar preferências quando houver mudanças
  useEffect(() => {
    salvarPreferencias({
      cafe: state.cafe,
      proporcao: state.proporcao,
      perfilSabor: state.perfilSabor,
      perfilCorpo: state.perfilCorpo,
      aguaBloqueada: state.aguaBloqueada,
      proporcaoBloqueada: state.proporcaoBloqueada
    });
  }, [state, salvarPreferencias]);

  // Função para atualizar café e recalcular água
  const handleCafeChange = useCallback((valor: string) => {
    const validation = validateCafeAmount(valor);
    const valorSanitizado = validation.sanitized;

    setState(prev => {
      const numCafe = parseFloat(valorSanitizado) || 0;
      const numProporcao = parseFloat(prev.proporcao) || 0;
      const novaAgua = numCafe > 0 && numProporcao > 0 ? arredondar(numCafe * numProporcao) : prev.agua;

      return {
        ...prev,
        cafe: valorSanitizado,
        agua: novaAgua
      };
    });
  }, [arredondar]);

  // Função para atualizar água e recalcular café
  const handleAguaChange = useCallback((valor: string) => {
    if (state.aguaBloqueada) return;

    const validation = validateAguaAmount(valor);
    const valorSanitizado = validation.sanitized;

    setState(prev => {
      const numAgua = parseFloat(valorSanitizado) || 0;
      const numProporcao = parseFloat(prev.proporcao) || 0;
      const novoCafe = numAgua > 0 && numProporcao > 0 ? arredondar(numAgua / numProporcao) : prev.cafe;

      return {
        ...prev,
        agua: valorSanitizado,
        cafe: novoCafe
      };
    });
  }, [state.aguaBloqueada, arredondar]);

  // Função para atualizar proporção e recalcular água
  const handleProporcaoChange = useCallback((valor: string) => {
    if (state.proporcaoBloqueada) return;

    const validation = validateProporcao(valor);
    const valorSanitizado = validation.sanitized;

    setState(prev => {
      const numCafe = parseFloat(prev.cafe) || 0;
      const numProporcao = parseFloat(valorSanitizado) || 0;
      const novaAgua = numCafe > 0 && numProporcao > 0 ? arredondar(numCafe * numProporcao) : prev.agua;

      return {
        ...prev,
        proporcao: valorSanitizado,
        agua: novaAgua
      };
    });
  }, [state.proporcaoBloqueada, arredondar]);

  const setPerfilSabor = useCallback((perfil: string) => {
    setState(prev => ({ ...prev, perfilSabor: perfil }));
  }, []);

  const setPerfilCorpo = useCallback((perfil: string) => {
    setState(prev => ({ ...prev, perfilCorpo: perfil }));
  }, []);

  const toggleAguaBloqueada = useCallback(() => {
    setState(prev => ({ ...prev, aguaBloqueada: !prev.aguaBloqueada }));
  }, []);

  const toggleProporcaoBloqueada = useCallback(() => {
    setState(prev => ({ ...prev, proporcaoBloqueada: !prev.proporcaoBloqueada }));
  }, []);



  return {
    state,
    handleCafeChange,
    handleAguaChange,
    handleProporcaoChange,
    setPerfilSabor,
    setPerfilCorpo,
    toggleAguaBloqueada,
    toggleProporcaoBloqueada,
    calcularReceita: () => calcularReceita
  };
}
