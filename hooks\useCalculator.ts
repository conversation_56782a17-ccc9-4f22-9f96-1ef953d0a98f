import { useState, useCallback, useEffect } from 'react';
import { CalculatorState, RecipeStep, PerfilSabor, PerfilCorpo } from '@/types/calculator';
import { DEFAULT_VALUES, RECIPE_TIMING } from '@/constants/app';
import { useLocalStorage } from './useLocalStorage';
import { validateCafeAmount, validateAguaAmount, validateProporcao } from '@/lib/validation';

export function useCalculator() {
  const [state, setState] = useState<CalculatorState>({
    cafe: DEFAULT_VALUES.cafe,
    agua: DEFAULT_VALUES.agua,
    proporcao: DEFAULT_VALUES.proporcao,
    perfilSabor: DEFAULT_VALUES.perfilSabor,
    perfilCorpo: DEFAULT_VALUES.perfilCorpo,
    aguaBloqueada: DEFAULT_VALUES.aguaBloqueada,
    proporcaoBloqueada: DEFAULT_VALUES.proporcaoBloqueada
  });

  const { salvarPreferencias, carregarPreferencias } = useLocalStorage();

  // Função para arredondar valores
  const arredondar = useCallback((valor: number): string => {
    return Math.round(valor).toString();
  }, []);

  // Carregar preferências ao inicializar
  useEffect(() => {
    const preferencias = carregarPreferencias();
    if (preferencias) {
      setState(prev => ({
        ...prev,
        ...preferencias
      }));

      // Recalcular água com os valores carregados
      const numCafe = parseFloat(preferencias.cafe);
      const numProporcao = parseFloat(preferencias.proporcao);
      if (numCafe > 0 && numProporcao > 0) {
        setState(prev => ({
          ...prev,
          agua: arredondar(numCafe * numProporcao)
        }));
      }
    }
  }, [carregarPreferencias, arredondar]);

  // Salvar preferências quando houver mudanças
  useEffect(() => {
    salvarPreferencias({
      cafe: state.cafe,
      proporcao: state.proporcao,
      perfilSabor: state.perfilSabor,
      perfilCorpo: state.perfilCorpo,
      aguaBloqueada: state.aguaBloqueada,
      proporcaoBloqueada: state.proporcaoBloqueada
    });
  }, [state, salvarPreferencias]);

  // Função para atualizar café e recalcular água
  const handleCafeChange = useCallback((valor: string) => {
    const validation = validateCafeAmount(valor);
    const valorSanitizado = validation.sanitized;

    setState(prev => {
      const numCafe = parseFloat(valorSanitizado) || 0;
      const numProporcao = parseFloat(prev.proporcao) || 0;
      const novaAgua = numCafe > 0 && numProporcao > 0 ? arredondar(numCafe * numProporcao) : prev.agua;

      return {
        ...prev,
        cafe: valorSanitizado,
        agua: novaAgua
      };
    });
  }, [arredondar]);

  // Função para atualizar água e recalcular café
  const handleAguaChange = useCallback((valor: string) => {
    if (state.aguaBloqueada) return;

    const validation = validateAguaAmount(valor);
    const valorSanitizado = validation.sanitized;

    setState(prev => {
      const numAgua = parseFloat(valorSanitizado) || 0;
      const numProporcao = parseFloat(prev.proporcao) || 0;
      const novoCafe = numAgua > 0 && numProporcao > 0 ? arredondar(numAgua / numProporcao) : prev.cafe;

      return {
        ...prev,
        agua: valorSanitizado,
        cafe: novoCafe
      };
    });
  }, [state.aguaBloqueada, arredondar]);

  // Função para atualizar proporção e recalcular água
  const handleProporcaoChange = useCallback((valor: string) => {
    if (state.proporcaoBloqueada) return;

    const validation = validateProporcao(valor);
    const valorSanitizado = validation.sanitized;

    setState(prev => {
      const numCafe = parseFloat(prev.cafe) || 0;
      const numProporcao = parseFloat(valorSanitizado) || 0;
      const novaAgua = numCafe > 0 && numProporcao > 0 ? arredondar(numCafe * numProporcao) : prev.agua;

      return {
        ...prev,
        proporcao: valorSanitizado,
        agua: novaAgua
      };
    });
  }, [state.proporcaoBloqueada, arredondar]);

  const setPerfilSabor = useCallback((perfil: PerfilSabor) => {
    setState(prev => ({ ...prev, perfilSabor: perfil }));
  }, []);

  const setPerfilCorpo = useCallback((perfil: PerfilCorpo) => {
    setState(prev => ({ ...prev, perfilCorpo: perfil }));
  }, []);

  const toggleAguaBloqueada = useCallback(() => {
    setState(prev => ({ ...prev, aguaBloqueada: !prev.aguaBloqueada }));
  }, []);

  const toggleProporcaoBloqueada = useCallback(() => {
    setState(prev => ({ ...prev, proporcaoBloqueada: !prev.proporcaoBloqueada }));
  }, []);

  // Função para calcular a receita baseada no método 4:6
  const calcularReceita = useCallback((): RecipeStep[] => {
    const aguaTotal = parseFloat(state.agua) || 0;
    if (aguaTotal <= 0) return [];

    const receita: RecipeStep[] = [];
    let aguaAcumulada = 0;

    // Primeiros 40% (2 despejos)
    const primeiros40 = aguaTotal * 0.4;
    let primeiro: number, segundo: number;

    if (state.perfilSabor === "Mais Acidez") {
      primeiro = Math.round(aguaTotal * 0.24);
      segundo = Math.round(aguaTotal * 0.16);
    } else if (state.perfilSabor === "Mais Doçura") {
      primeiro = Math.round(aguaTotal * 0.16);
      segundo = Math.round(aguaTotal * 0.24);
    } else { // Equilibrado
      primeiro = Math.round(primeiros40 / 2);
      segundo = Math.round(primeiros40 / 2);
    }

    // Adicionar primeiros dois despejos
    aguaAcumulada += primeiro;
    receita.push({
      tempo: "00:00",
      tempoSegundos: RECIPE_TIMING.firstPour,
      quantidade: primeiro,
      total: aguaAcumulada
    });

    aguaAcumulada += segundo;
    receita.push({
      tempo: "00:45",
      tempoSegundos: RECIPE_TIMING.secondPour,
      quantidade: segundo,
      total: aguaAcumulada
    });

    // Últimos 60%
    const ultimos60 = aguaTotal - aguaAcumulada;
    let despejos: number[] = [];

    if (state.perfilCorpo === "Menos Corpo") {
      // 2 despejos (30% cada)
      const despejo = Math.round(ultimos60 / 2);
      despejos = [despejo, ultimos60 - despejo];
    } else if (state.perfilCorpo === "Mais Corpo") {
      // 4 despejos (15% cada)
      const despejo = Math.round(ultimos60 / 4);
      despejos = [despejo, despejo, despejo, ultimos60 - (despejo * 3)];
    } else { // Equilibrado
      // 3 despejos (20% cada)
      const despejo = Math.round(ultimos60 / 3);
      despejos = [despejo, despejo, ultimos60 - (despejo * 2)];
    }

    // Adicionar despejos restantes
    const tempos = ["01:30", "02:15", "03:00", "03:45"];
    despejos.forEach((quantidade, index) => {
      aguaAcumulada += quantidade;
      receita.push({
        tempo: tempos[index],
        tempoSegundos: RECIPE_TIMING.subsequentPours[index],
        quantidade,
        total: aguaAcumulada
      });
    });

    return receita;
  }, [state.agua, state.perfilSabor, state.perfilCorpo]);

  return {
    state,
    handleCafeChange,
    handleAguaChange,
    handleProporcaoChange,
    setPerfilSabor,
    setPerfilCorpo,
    toggleAguaBloqueada,
    toggleProporcaoBloqueada,
    calcularReceita
  };
}
