import { useState, useCallback, useEffect } from 'react';

interface AdvancedPreferences {
  // Preferências de áudio
  audioEnabled: boolean;
  audioVolume: number;
  
  // Preferências de interface
  animationsEnabled: boolean;
  autoScrollEnabled: boolean;
  compactMode: boolean;
  
  // Preferências de performance
  cacheEnabled: boolean;
  debugMode: boolean;
  
  // Preferências de acessibilidade
  highContrastMode: boolean;
  reducedMotion: boolean;
  screenReaderOptimized: boolean;
}

const DEFAULT_ADVANCED_PREFERENCES: AdvancedPreferences = {
  audioEnabled: true,
  audioVolume: 0.5,
  animationsEnabled: true,
  autoScrollEnabled: true,
  compactMode: false,
  cacheEnabled: true,
  debugMode: false,
  highContrastMode: false,
  reducedMotion: false,
  screenReaderOptimized: false
};

const STORAGE_KEY = 'cereja-advanced-preferences';

export function useAdvancedPreferences() {
  const [preferences, setPreferences] = useState<AdvancedPreferences>(DEFAULT_ADVANCED_PREFERENCES);

  // Carregar preferências do localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        setPreferences(prev => ({ ...prev, ...parsed }));
      }
    } catch (error) {
      console.error('Erro ao carregar preferências avançadas:', error);
    }
  }, []);

  // Salvar preferências no localStorage
  const savePreferences = useCallback((newPreferences: Partial<AdvancedPreferences>) => {
    setPreferences(prev => {
      const updated = { ...prev, ...newPreferences };
      
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
      } catch (error) {
        console.error('Erro ao salvar preferências avançadas:', error);
      }
      
      return updated;
    });
  }, []);

  // Resetar para padrões
  const resetToDefaults = useCallback(() => {
    setPreferences(DEFAULT_ADVANCED_PREFERENCES);
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('Erro ao resetar preferências:', error);
    }
  }, []);

  // Detectar preferências do sistema
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Detectar preferência de movimento reduzido
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
      if (prefersReducedMotion && !preferences.reducedMotion) {
        savePreferences({ reducedMotion: true, animationsEnabled: false });
      }

      // Detectar preferência de alto contraste
      const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches;
      if (prefersHighContrast && !preferences.highContrastMode) {
        savePreferences({ highContrastMode: true });
      }
    }
  }, [preferences.reducedMotion, preferences.highContrastMode, savePreferences]);

  return {
    preferences,
    savePreferences,
    resetToDefaults,
    updatePreference: useCallback((key: keyof AdvancedPreferences, value: any) => {
      savePreferences({ [key]: value });
    }, [savePreferences])
  };
}
