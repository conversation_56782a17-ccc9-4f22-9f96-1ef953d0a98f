import { trackRecipeShared, trackButtonClick } from '@/lib/analytics';
import { RecipeStep } from '@/types/calculator';

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  calculator: {
    state: {
      cafe: string;
      agua: string;
      proporcao: string;
      perfilSabor: string;
      perfilCorpo: string;
    };
    calcularReceita: () => RecipeStep[];
  };
}

export function ShareModal({ isOpen, onClose, calculator }: ShareModalProps) {
  if (!isOpen) return null;

  const handleCopyRecipe = () => {
    const receita = calculator.calcularReceita();
    const texto = `☕ Receita Cereja - Método 4:6\n\n` +
      `📊 Configuração:\n` +
      `• ${calculator.state.cafe}g de café\n` +
      `• ${calculator.state.agua}ml de água\n` +
      `• Proporção 1:${calculator.state.proporcao}\n` +
      `• Perfil: ${calculator.state.perfilSabor} / ${calculator.state.perfilCorpo}\n\n` +
      `⏱️ Passos:\n` +
      receita.map((passo, i) =>
        `${i + 1}. ${passo.tempo} - ${passo.quantidade}ml (Total: ${passo.total}ml)`
      ).join('\n') +
      `\n\n🌐 cereja-app.vercel.app`;

    const copyToClipboard = async () => {
      try {
        if (navigator.clipboard && navigator.clipboard.writeText) {
          await navigator.clipboard.writeText(texto);
        } else {
          // Fallback para navegadores mais antigos
          const textArea = document.createElement('textarea');
          textArea.value = texto;
          textArea.style.position = 'fixed';
          textArea.style.left = '-999999px';
          textArea.style.top = '-999999px';
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
        }

        trackRecipeShared('copy');
        trackButtonClick('copiar_receita', 'modal_compartilhar');
        onClose();
        
        // Mostrar feedback visual
        const feedback = document.createElement('div');
        feedback.textContent = 'Receita copiada! ✅';
        feedback.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground px-4 py-2 rounded-lg shadow-lg z-50 animate-in slide-in-from-top duration-300';
        document.body.appendChild(feedback);
        setTimeout(() => {
          feedback.remove();
        }, 3000);
      } catch (error) {
        console.error('Erro ao copiar receita:', error);
        alert('Não foi possível copiar a receita.');
      }
    };

    copyToClipboard();
  };

  const handleCancel = () => {
    onClose();
    trackButtonClick('cancelar_compartilhar', 'modal_compartilhar');
  };

  return (
    <div 
      className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50 animate-in fade-in duration-300"
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
    >
      <div className="bg-card border border-border rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-y-auto animate-in zoom-in-95 duration-300">
        {/* Header do Modal */}
        <div className="flex items-center justify-between p-4 border-b border-border">
          <h3 id="modal-title" className="text-lg font-semibold text-card-foreground">
            Compartilhar Receita
          </h3>
          <button
            onClick={onClose}
            aria-label="Fechar modal"
            className="p-1 rounded-md hover:bg-accent transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
          </button>
        </div>

        {/* Conteúdo do Modal */}
        <div className="p-4 space-y-4">
          {/* Preview da Receita */}
          <div className="bg-secondary rounded-lg p-4 text-sm">
            <div className="font-semibold text-secondary-foreground mb-2">
              ☕ Receita Cereja - Método 4:6
            </div>
            <div className="space-y-2 text-muted-foreground">
              <div>
                <strong>📊 Configuração:</strong>
                <div className="ml-2">
                  • {calculator.state.cafe}g de café<br/>
                  • {calculator.state.agua}ml de água<br/>
                  • Proporção 1:{calculator.state.proporcao}<br/>
                  • Perfil: {calculator.state.perfilSabor} / {calculator.state.perfilCorpo}
                </div>
              </div>
              <div>
                <strong>⏱️ Passos:</strong>
                <div className="ml-2">
                  {calculator.calcularReceita().map((passo, i) => (
                    <div key={i}>
                      {i + 1}. {passo.tempo} - {passo.quantidade}ml (Total: {passo.total}ml)
                    </div>
                  ))}
                </div>
              </div>
              <div className="text-xs text-muted-foreground">
                🌐 cereja-app.vercel.app
              </div>
            </div>
          </div>

          {/* Botões de Ação */}
          <div className="flex gap-2">
            <button
              onClick={handleCopyRecipe}
              className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-primary text-primary-foreground rounded-md text-sm font-medium hover:bg-primary/90 transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true">
                <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
              </svg>
              Copiar Receita
            </button>
            <button
              onClick={handleCancel}
              className="px-4 py-3 bg-secondary text-secondary-foreground rounded-md text-sm font-medium hover:bg-accent hover:text-accent-foreground transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            >
              Cancelar
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
