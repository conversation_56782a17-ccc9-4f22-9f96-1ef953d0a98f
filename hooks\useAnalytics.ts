import { useCallback, useEffect } from 'react';
import { useAdvancedPreferences } from './useAdvancedPreferences';

interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
  timestamp: number;
  sessionId: string;
}

class AnalyticsManager {
  private events: AnalyticsEvent[] = [];
  private sessionId: string;
  private maxEvents = 100;

  constructor() {
    this.sessionId = this.generateSessionId();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  track(name: string, properties?: Record<string, any>) {
    const event: AnalyticsEvent = {
      name,
      properties,
      timestamp: Date.now(),
      sessionId: this.sessionId
    };

    this.events.push(event);

    // Manter apenas os últimos eventos
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    // Log em desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Analytics Event:', event);
    }
  }

  getEvents(): AnalyticsEvent[] {
    return [...this.events];
  }

  getSessionStats() {
    const now = Date.now();
    const sessionStart = this.events.length > 0 ? this.events[0].timestamp : now;
    const sessionDuration = now - sessionStart;

    return {
      sessionId: this.sessionId,
      sessionDuration,
      eventCount: this.events.length,
      sessionStart: new Date(sessionStart).toISOString()
    };
  }

  clear() {
    this.events = [];
  }
}

const analyticsManager = new AnalyticsManager();

export function useAnalytics() {
  const { preferences } = useAdvancedPreferences();

  const track = useCallback((name: string, properties?: Record<string, any>) => {
    // Só rastrear se o debug mode estiver habilitado ou em desenvolvimento
    if (preferences.debugMode || process.env.NODE_ENV === 'development') {
      analyticsManager.track(name, properties);
    }
  }, [preferences.debugMode]);

  const trackPerformance = useCallback((name: string, duration: number, metadata?: Record<string, any>) => {
    track('performance', {
      operation: name,
      duration,
      ...metadata
    });
  }, [track]);

  const trackError = useCallback((error: Error, context?: string) => {
    track('error', {
      message: error.message,
      stack: error.stack,
      context,
      userAgent: navigator.userAgent
    });
  }, [track]);

  const trackUserInteraction = useCallback((action: string, element: string, metadata?: Record<string, any>) => {
    track('user_interaction', {
      action,
      element,
      ...metadata
    });
  }, [track]);

  const getSessionStats = useCallback(() => {
    return analyticsManager.getSessionStats();
  }, []);

  const getEvents = useCallback(() => {
    return analyticsManager.getEvents();
  }, []);

  const exportData = useCallback(() => {
    const data = {
      session: getSessionStats(),
      events: getEvents(),
      preferences: preferences,
      timestamp: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `cereja-analytics-${data.session.sessionId}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }, [getSessionStats, getEvents, preferences]);

  // Rastrear eventos de performance automaticamente
  useEffect(() => {
    if (preferences.debugMode) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            trackPerformance('page_load', entry.duration, {
              type: navEntry.type,
              redirectCount: navEntry.redirectCount
            });
          }
        }
      });

      observer.observe({ entryTypes: ['navigation'] });

      return () => observer.disconnect();
    }
  }, [preferences.debugMode, trackPerformance]);

  return {
    track,
    trackPerformance,
    trackError,
    trackUserInteraction,
    getSessionStats,
    getEvents,
    exportData
  };
}
