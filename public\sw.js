const CACHE_NAME = 'cereja-v1';
const STATIC_CACHE_NAME = 'cereja-static-v1';
const DYNAMIC_CACHE_NAME = 'cereja-dynamic-v1';

// Recursos para cache estático (sempre em cache)
const STATIC_ASSETS = [
  '/',
  '/cereja.png',
  '/images/equilibrado.png',
  '/images/acido.png',
  '/images/docura.png',
  '/images/menos-corpo.png',
  '/images/mais-corpo.png',
  '/audio/countdown.mp3',
  '/audio/finish.mp3',
  '/audio/transition.mp3'
];

// Recursos para cache dinâmico
const CACHE_STRATEGIES = {
  // Cache first para recursos estáticos
  static: [
    /\.(js|css|woff2?|png|jpg|jpeg|gif|svg|ico)$/,
    /^\/images\//,
    /^\/audio\//
  ],
  
  // Network first para páginas
  pages: [
    /^\/$/,
    /^\/glossario/,
    /^\/privacidade/
  ]
};

// Instalar Service Worker
self.addEventListener('install', event => {
  console.log('🔧 Service Worker installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then(cache => {
        console.log('📦 Caching static assets...');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('✅ Static assets cached');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('❌ Error caching static assets:', error);
      })
  );
});

// Ativar Service Worker
self.addEventListener('activate', event => {
  console.log('🚀 Service Worker activating...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== STATIC_CACHE_NAME && cacheName !== DYNAMIC_CACHE_NAME) {
              console.log('🗑️ Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('✅ Service Worker activated');
        return self.clients.claim();
      })
  );
});

// Interceptar requisições
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Ignorar requisições não-GET
  if (request.method !== 'GET') return;
  
  // Ignorar requisições para outros domínios
  if (url.origin !== location.origin) return;
  
  // Estratégia de cache baseada no tipo de recurso
  if (isStaticAsset(request.url)) {
    event.respondWith(cacheFirst(request));
  } else if (isPageRequest(request.url)) {
    event.respondWith(networkFirst(request));
  } else {
    event.respondWith(staleWhileRevalidate(request));
  }
});

// Verificar se é um recurso estático
function isStaticAsset(url) {
  return CACHE_STRATEGIES.static.some(pattern => pattern.test(url));
}

// Verificar se é uma página
function isPageRequest(url) {
  return CACHE_STRATEGIES.pages.some(pattern => pattern.test(url));
}

// Estratégia Cache First
async function cacheFirst(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('Cache first failed:', error);
    return new Response('Offline', { status: 503 });
  }
}

// Estratégia Network First
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.log('Network failed, trying cache:', error);
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Fallback para página offline
    return new Response(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Cereja - Offline</title>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { font-family: system-ui; text-align: center; padding: 2rem; }
            .offline { color: #666; }
          </style>
        </head>
        <body>
          <h1>🔌 Você está offline</h1>
          <p class="offline">Verifique sua conexão com a internet e tente novamente.</p>
          <button onclick="window.location.reload()">Tentar Novamente</button>
        </body>
      </html>
    `, {
      status: 200,
      headers: { 'Content-Type': 'text/html' }
    });
  }
}

// Estratégia Stale While Revalidate
async function staleWhileRevalidate(request) {
  const cache = await caches.open(DYNAMIC_CACHE_NAME);
  const cachedResponse = await cache.match(request);
  
  const fetchPromise = fetch(request).then(networkResponse => {
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  }).catch(() => cachedResponse);
  
  return cachedResponse || fetchPromise;
}

// Limpar cache antigo periodicamente
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'CLEAN_CACHE') {
    cleanOldCache();
  }
});

async function cleanOldCache() {
  const cache = await caches.open(DYNAMIC_CACHE_NAME);
  const requests = await cache.keys();
  
  // Manter apenas os últimos 50 itens
  if (requests.length > 50) {
    const toDelete = requests.slice(0, requests.length - 50);
    await Promise.all(toDelete.map(request => cache.delete(request)));
    console.log(`🧹 Cleaned ${toDelete.length} old cache entries`);
  }
}
