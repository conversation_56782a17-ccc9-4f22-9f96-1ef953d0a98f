import { useMemo } from 'react';
import { RecipeStep } from '@/types/calculator';

interface UseOptimizedCalculationsProps {
  agua: string;
  perfilSabor: string;
  perfilCorpo: string;
}

export function useOptimizedCalculations({
  agua,
  perfilSabor,
  perfilCorpo
}: UseOptimizedCalculationsProps) {
  // Memoizar o cálculo da receita para evitar recálculos desnecessários
  const receita = useMemo((): RecipeStep[] => {
    const aguaTotal = parseFloat(agua) || 0;
    if (aguaTotal <= 0) return [];

    const receitaCalculada: RecipeStep[] = [];
    let aguaAcumulada = 0;

    // Primeiros 40% (2 despejos)
    const primeiros40 = aguaTotal * 0.4;
    let primeiro: number, segundo: number;

    if (perfilSabor === "Mais Acidez") {
      primeiro = Math.round(aguaTotal * 0.24);
      segundo = Math.round(aguaTotal * 0.16);
    } else if (perfilSabor === "Mais Doçura") {
      primeiro = Math.round(aguaTotal * 0.16);
      segundo = Math.round(aguaTotal * 0.24);
    } else { // Equilibrado
      primeiro = Math.round(primeiros40 / 2);
      segundo = Math.round(primeiros40 / 2);
    }

    // Adicionar primeiros dois despejos
    aguaAcumulada += primeiro;
    receitaCalculada.push({
      tempo: "00:00",
      tempoSegundos: 0,
      quantidade: primeiro,
      total: aguaAcumulada
    });

    aguaAcumulada += segundo;
    receitaCalculada.push({
      tempo: "00:45",
      tempoSegundos: 45,
      quantidade: segundo,
      total: aguaAcumulada
    });

    // Últimos 60%
    const ultimos60 = aguaTotal - aguaAcumulada;
    let despejos: number[] = [];

    if (perfilCorpo === "Menos Corpo") {
      // 2 despejos (30% cada)
      const despejo = Math.round(ultimos60 / 2);
      despejos = [despejo, ultimos60 - despejo];
    } else if (perfilCorpo === "Mais Corpo") {
      // 4 despejos (15% cada)
      const despejo = Math.round(ultimos60 / 4);
      despejos = [despejo, despejo, despejo, ultimos60 - (despejo * 3)];
    } else { // Equilibrado
      // 3 despejos (20% cada)
      const despejo = Math.round(ultimos60 / 3);
      despejos = [despejo, despejo, ultimos60 - (despejo * 2)];
    }

    // Adicionar despejos restantes
    const tempos = ["01:30", "02:15", "03:00", "03:45"];
    const temposSegundos = [90, 135, 180, 225];
    despejos.forEach((quantidade, index) => {
      aguaAcumulada += quantidade;
      receitaCalculada.push({
        tempo: tempos[index],
        tempoSegundos: temposSegundos[index],
        quantidade,
        total: aguaAcumulada
      });
    });

    return receitaCalculada;
  }, [agua, perfilSabor, perfilCorpo]);

  // Memoizar o tempo total
  const tempoTotal = useMemo(() => {
    if (receita.length === 0) return 0;
    const ultimoPasso = receita[receita.length - 1];
    return ultimoPasso.tempoSegundos + 45;
  }, [receita]);

  // Memoizar informações da receita para compartilhamento
  const recipeInfo = useMemo(() => ({
    totalSteps: receita.length,
    totalWater: receita.length > 0 ? receita[receita.length - 1].total : 0,
    duration: tempoTotal
  }), [receita, tempoTotal]);

  return {
    receita,
    tempoTotal,
    recipeInfo
  };
}
