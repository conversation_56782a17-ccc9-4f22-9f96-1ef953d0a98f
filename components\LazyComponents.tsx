import { lazy } from 'react';

// Lazy loading para componentes pesados
export const LazyShareModal = lazy(() => 
  import('./calculator/ShareModal').then(module => ({ default: module.ShareModal }))
);

export const LazyNavigationSidebar = lazy(() => 
  import('./calculator/NavigationSidebar').then(module => ({ default: module.NavigationSidebar }))
);

// Componente de loading
export function ComponentLoader() {
  return (
    <div className="flex items-center justify-center p-4">
      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
    </div>
  );
}
