import React, { useRef } from 'react';
import { TimerDisplay } from './TimerDisplay';
import { TimerControls } from './TimerControls';
import { ProgressBar } from './ProgressBar';
import { RecipeSteps } from './RecipeSteps';

interface TimerSectionProps {
  timer: {
    state: {
      tempo: number;
      rodando: boolean;
      contandoRegressivo: boolean;
      contagemRegressiva: number;
    };
    calcularTempoTotal: () => number;
    obterPassoAtual: () => number;
    formatarTempo: (segundos: number) => string;
    iniciarPausar: () => void;
    resetar: () => void;
  };
  calculator: {
    calcularReceita: () => any[];
  };
  onOpenModal: () => void;
  receitaRef: React.RefObject<HTMLDivElement>;
}

export function TimerSection({ timer, calculator, onOpenModal, receitaRef }: TimerSectionProps) {
  const tempoTotal = timer.calcularTempoTotal();
  const receitaFinalizada = timer.state.tempo >= tempoTotal && !timer.state.contandoRegressivo;

  return (
    <div className="bg-card p-4 sm:p-6 rounded-lg border border-border shadow-md">
      <h2 className="text-xs sm:text-sm font-medium text-foreground mb-3 sm:mb-4">
        Cronômetro
      </h2>

      <TimerDisplay
        tempo={timer.state.tempo}
        contagemRegressiva={timer.state.contagemRegressiva}
        contandoRegressivo={timer.state.contandoRegressivo}
        rodando={timer.state.rodando}
        tempoTotal={tempoTotal}
        formatarTempo={timer.formatarTempo}
        receitaFinalizada={receitaFinalizada}
      />

      <TimerControls
        tempo={timer.state.tempo}
        rodando={timer.state.rodando}
        contandoRegressivo={timer.state.contandoRegressivo}
        receitaFinalizada={receitaFinalizada}
        onIniciarPausar={timer.iniciarPausar}
        onResetar={timer.resetar}
        onOpenModal={onOpenModal}
      />

      <ProgressBar
        tempo={timer.state.tempo}
        rodando={timer.state.rodando}
        contandoRegressivo={timer.state.contandoRegressivo}
        tempoTotal={tempoTotal}
        receita={calculator.calcularReceita()}
        obterPassoAtual={timer.obterPassoAtual}
      />

      <RecipeSteps
        ref={receitaRef}
        tempo={timer.state.tempo}
        contandoRegressivo={timer.state.contandoRegressivo}
        tempoTotal={tempoTotal}
        receita={calculator.calcularReceita()}
        obterPassoAtual={timer.obterPassoAtual}
      />
    </div>
  );
}
