// Validação de dados com sanitização

export function sanitizeNumericInput(value: string): string {
  // Remove caracteres não numéricos exceto ponto e vírgula
  const cleaned = value.replace(/[^0-9.,]/g, '');
  // Substitui vírgula por ponto
  const normalized = cleaned.replace(',', '.');
  // Remove pontos extras (mantém apenas o primeiro)
  const parts = normalized.split('.');
  if (parts.length > 2) {
    return parts[0] + '.' + parts.slice(1).join('');
  }
  return normalized;
}

export function validateNumericRange(value: string, min: number = 0, max: number = 10000): boolean {
  const num = parseFloat(value);
  return !isNaN(num) && num >= min && num <= max;
}

export function validateCafeAmount(value: string): { isValid: boolean; sanitized: string; error?: string } {
  const sanitized = sanitizeNumericInput(value);
  
  if (!sanitized) {
    return { isValid: false, sanitized, error: 'Quantidade de café é obrigatória' };
  }
  
  if (!validateNumericRange(sanitized, 1, 1000)) {
    return { isValid: false, sanitized, error: 'Quantidade de café deve estar entre 1g e 1000g' };
  }
  
  return { isValid: true, sanitized };
}

export function validateAguaAmount(value: string): { isValid: boolean; sanitized: string; error?: string } {
  const sanitized = sanitizeNumericInput(value);
  
  if (!sanitized) {
    return { isValid: false, sanitized, error: 'Quantidade de água é obrigatória' };
  }
  
  if (!validateNumericRange(sanitized, 10, 10000)) {
    return { isValid: false, sanitized, error: 'Quantidade de água deve estar entre 10ml e 10000ml' };
  }
  
  return { isValid: true, sanitized };
}

export function validateProporcao(value: string): { isValid: boolean; sanitized: string; error?: string } {
  const sanitized = sanitizeNumericInput(value);
  
  if (!sanitized) {
    return { isValid: false, sanitized, error: 'Proporção é obrigatória' };
  }
  
  if (!validateNumericRange(sanitized, 5, 50)) {
    return { isValid: false, sanitized, error: 'Proporção deve estar entre 1:5 e 1:50' };
  }
  
  return { isValid: true, sanitized };
}
