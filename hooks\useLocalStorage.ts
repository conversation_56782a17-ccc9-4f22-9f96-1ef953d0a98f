import { useCallback } from 'react';
import { UserPreferences } from '@/types/calculator';
import { STORAGE_KEYS, DEFAULT_VALUES } from '@/constants/app';

export function useLocalStorage() {
  const salvarPreferencias = useCallback((preferencias: UserPreferences) => {
    try {
      localStorage.setItem(STORAGE_KEYS.preferences, JSON.stringify(preferencias));
    } catch (error) {
      console.error('Erro ao salvar preferências:', error);
    }
  }, []);

  const carregarPreferencias = useCallback((): UserPreferences | null => {
    try {
      const preferenciasString = localStorage.getItem(STORAGE_KEYS.preferences);
      if (preferenciasString) {
        const preferencias = JSON.parse(preferenciasString);
        return {
          cafe: preferencias.cafe || DEFAULT_VALUES.cafe,
          proporcao: preferencias.proporcao || DEFAULT_VALUES.proporcao,
          perfilSabor: preferencias.perfilSabor || DEFAULT_VALUES.perfilSabor,
          perfilCorpo: preferencias.perfilCorpo || DEFAULT_VALUES.perfilCorpo,
          aguaBloqueada: preferencias.aguaBloqueada ?? DEFAULT_VALUES.aguaBloqueada,
          proporcaoBloqueada: preferencias.proporcaoBloqueada ?? DEFAULT_VALUES.proporcaoBloqueada
        };
      }
      return null;
    } catch (error) {
      console.error('Erro ao carregar preferências:', error);
      return null;
    }
  }, []);

  return {
    salvarPreferencias,
    carregarPreferencias
  };
}
