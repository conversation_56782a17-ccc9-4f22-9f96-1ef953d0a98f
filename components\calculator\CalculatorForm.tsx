import { NumericInputs } from './NumericInputs';
import { ProfileSelector } from './ProfileSelector';
import { PROFILE_OPTIONS } from '@/constants/app';

interface CalculatorFormProps {
  calculator: {
    state: {
      cafe: string;
      agua: string;
      proporcao: string;
      perfilSabor: string;
      perfilCorpo: string;
      aguaBloqueada: boolean;
      proporcaoBloqueada: boolean;
    };
    handleCafeChange: (valor: string) => void;
    handleAguaChange: (valor: string) => void;
    handleProporcaoChange: (valor: string) => void;
    setPerfilSabor: (perfil: string) => void;
    setPerfilCorpo: (perfil: string) => void;
    toggleAguaBloqueada: () => void;
    toggleProporcaoBloqueada: () => void;
  };
  inputsDesabilitados: () => boolean;
}

export function CalculatorForm({ calculator, inputsDesabilitados }: CalculatorFormProps) {
  return (
    <div className="bg-card p-4 sm:p-6 rounded-lg border border-border shadow-md">
      <NumericInputs
        cafe={calculator.state.cafe}
        agua={calculator.state.agua}
        proporcao={calculator.state.proporcao}
        aguaBloqueada={calculator.state.aguaBloqueada}
        proporcaoBloqueada={calculator.state.proporcaoBloqueada}
        onCafeChange={calculator.handleCafeChange}
        onAguaChange={calculator.handleAguaChange}
        onProporcaoChange={calculator.handleProporcaoChange}
        onToggleAguaBloqueada={calculator.toggleAguaBloqueada}
        onToggleProporcaoBloqueada={calculator.toggleProporcaoBloqueada}
        inputsDesabilitados={inputsDesabilitados}
      />

      <ProfileSelector
        title="Perfil de Sabor"
        options={PROFILE_OPTIONS.sabor}
        selectedValue={calculator.state.perfilSabor}
        onSelect={calculator.setPerfilSabor}
        inputsDesabilitados={inputsDesabilitados}
        className="mb-4 sm:mb-6"
      />

      <ProfileSelector
        title="Perfil de Corpo"
        options={PROFILE_OPTIONS.corpo}
        selectedValue={calculator.state.perfilCorpo}
        onSelect={calculator.setPerfilCorpo}
        inputsDesabilitados={inputsDesabilitados}
      />
    </div>
  );
}
