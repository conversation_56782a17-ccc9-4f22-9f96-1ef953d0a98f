import { Menu } from "lucide-react";

interface HeaderProps {
  onOpenSidebar: () => void;
}

export function Header({ onOpenSidebar }: HeaderProps) {
  return (
    <header className="fixed sm:absolute top-4 left-4 sm:top-6 sm:left-6 z-30">
      <button
        onClick={onOpenSidebar}
        aria-label="Abrir menu de navegação"
        className="flex items-center gap-2 p-2 text-muted-foreground hover:text-primary hover:bg-accent/50 rounded-lg sm:rounded-lg rounded-full sm:bg-transparent bg-card/90 backdrop-blur-sm shadow-lg sm:shadow-none border sm:border-0 border-border/20 transition-all focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
      >
        <Menu size={20} />
        <span className="text-sm font-medium hidden sm:inline">Menu</span>
      </button>
    </header>
  );
}
