"use client";

import { useEffect } from 'react';
import { AUDIO_FILES } from '@/constants/audio';

export function ResourcePreloader() {
  useEffect(() => {
    // Preload de áudios críticos
    const preloadAudio = (src: string) => {
      const audio = new Audio();
      audio.preload = 'auto';
      audio.src = src;
      // Não precisamos fazer nada com o áudio, apenas garantir que seja carregado
    };

    // Preload de imagens críticas
    const preloadImage = (src: string) => {
      const img = new Image();
      img.src = src;
    };

    // Preload de recursos críticos
    const criticalResources = [
      // Áudios
      AUDIO_FILES.countdown,
      AUDIO_FILES.finish,
      AUDIO_FILES.transition,
      
      // Imagens críticas
      '/cereja.png',
      '/images/equilibrado.png',
      '/images/acido.png',
      '/images/docura.png',
      '/images/menos-corpo.png',
      '/images/mais-corpo.png'
    ];

    criticalResources.forEach(resource => {
      if (resource.endsWith('.mp3')) {
        preloadAudio(resource);
      } else {
        preloadImage(resource);
      }
    });

    // Preload de fontes (se necessário)
    if ('fonts' in document) {
      const documentWithFonts = document as Document & { fonts?: { ready: Promise<void> } };
      if (documentWithFonts.fonts) {
        documentWithFonts.fonts.ready.then(() => {
          console.log('🔤 Fonts loaded');
        });
      }
    }

  }, []);

  return null; // Este componente não renderiza nada
}
