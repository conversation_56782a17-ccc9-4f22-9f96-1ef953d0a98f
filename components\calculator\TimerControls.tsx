import { Play, Pause, RotateCcw } from "lucide-react";
import { trackButtonClick } from '@/lib/analytics';

interface TimerControlsProps {
  tempo: number;
  rodando: boolean;
  contandoRegressivo: boolean;
  receitaFinalizada: boolean;
  onIniciarPausar: () => void;
  onResetar: () => void;
  onOpenModal: () => void;
}

export function TimerControls({
  tempo,
  rodando,
  contandoRegressivo,
  receitaFinalizada,
  onIniciarPausar,
  onResetar,
  onOpenModal
}: TimerControlsProps) {
  const handleOpenModal = () => {
    onOpenModal();
    trackButtonClick('abrir_compartilhar', 'calculadora');
  };

  return (
    <div className="flex justify-center gap-2 sm:gap-3">
      {receitaFinalizada ? (
        // Receita finalizada: botões Compartilhar e Reiniciar
        <>
          <button
            onClick={handleOpenModal}
            aria-label="Compartilhar receita"
            className="flex items-center gap-2 px-4 py-3 sm:py-2 bg-secondary text-secondary-foreground rounded-md text-xs font-normal hover:bg-accent hover:text-accent-foreground transition-colors min-h-touch touch-target focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
          >
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="sm:w-4 sm:h-4" aria-hidden="true">
              <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/>
              <polyline points="16,6 12,2 8,6"/>
              <line x1="12" y1="2" x2="12" y2="15"/>
            </svg>
            Compartilhar
          </button>
          <button
            onClick={onResetar}
            aria-label="Reiniciar cronômetro"
            className="flex items-center gap-2 px-4 py-3 sm:py-2 bg-primary text-primary-foreground rounded-md text-xs font-normal hover:bg-primary/90 transition-colors min-h-touch touch-target focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
          >
            <RotateCcw size={18} className="sm:w-4 sm:h-4" aria-hidden="true" />
            Reiniciar
          </button>
        </>
      ) : (
        // Receita em andamento: botões normais
        <>
          <button
            onClick={onIniciarPausar}
            aria-label={
              rodando ? "Pausar cronômetro" :
              contandoRegressivo ? "Cancelar contagem regressiva" :
              tempo > 0 ? "Retomar cronômetro" : "Iniciar cronômetro"
            }
            className="flex items-center gap-2 px-4 py-3 sm:py-2 bg-primary text-primary-foreground rounded-md text-xs font-normal hover:bg-primary/90 transition-colors min-h-touch touch-target focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
          >
            {rodando ? (
              <>
                <Pause size={18} className="sm:w-4 sm:h-4" aria-hidden="true" />
                Pausar
              </>
            ) : contandoRegressivo ? (
              <>
                <Pause size={18} className="sm:w-4 sm:h-4" aria-hidden="true" />
                Cancelar
              </>
            ) : tempo > 0 ? (
              <>
                <Play size={18} className="sm:w-4 sm:h-4" aria-hidden="true" />
                Retomar
              </>
            ) : (
              <>
                <Play size={18} className="sm:w-4 sm:h-4" aria-hidden="true" />
                Iniciar
              </>
            )}
          </button>

          {(tempo > 0 || rodando) && !contandoRegressivo && (
            <button
              onClick={onResetar}
              aria-label="Reiniciar cronômetro"
              className="flex items-center gap-2 px-4 py-3 sm:py-2 bg-secondary text-secondary-foreground rounded-md text-xs font-normal hover:bg-accent hover:text-accent-foreground transition-colors min-h-touch touch-target focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            >
              <RotateCcw size={18} className="sm:w-4 sm:h-4" aria-hidden="true" />
              Reiniciar
            </button>
          )}
        </>
      )}
    </div>
  );
}
