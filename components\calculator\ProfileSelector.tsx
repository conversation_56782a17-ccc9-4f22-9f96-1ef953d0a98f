import Image from "next/image";
import { ProfileOption } from '@/types/calculator';

interface ProfileSelectorProps {
  title: string;
  options: readonly ProfileOption[];
  selectedValue: string;
  onSelect: (value: string) => void;
  inputsDesabilitados: () => boolean;
  className?: string;
}

export function ProfileSelector({
  title,
  options,
  selectedValue,
  onSelect,
  inputsDesabilitados,
  className = ""
}: ProfileSelectorProps) {
  const isDisabled = inputsDesabilitados();

  return (
    <div className={className}>
      <h3 className="text-xs sm:text-sm font-medium text-foreground mb-2 sm:mb-3">
        {title}
      </h3>
      <div className="grid grid-cols-3 gap-2 sm:gap-3">
        {options.map((opcao) => (
          <button
            key={opcao.nome}
            onClick={() => !isDisabled && onSelect(opcao.nome)}
            disabled={isDisabled}
            aria-label={`Selecionar ${opcao.nome} para ${title.toLowerCase()}`}
            aria-pressed={selectedValue === opcao.nome}
            className={`px-3 sm:px-8 py-3 sm:py-4 rounded-md text-xs sm:text-sm font-medium transition-colors flex flex-col items-center gap-2 min-h-touch touch-target focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
              isDisabled
                ? "cursor-not-allowed opacity-50"
                : "cursor-pointer"
            } ${
              selectedValue === opcao.nome
                ? "bg-primary text-primary-foreground shadow-sm"
                : "bg-secondary text-secondary-foreground hover:bg-accent hover:text-accent-foreground shadow-xs hover:shadow-sm"
            }`}
          >
            <Image
              src={opcao.icone}
              alt={opcao.alt}
              width={32}
              height={32}
              className="object-contain sm:w-[30px] sm:h-[30px]"
              quality={75}
            />
            {opcao.nome}
          </button>
        ))}
      </div>
    </div>
  );
}
