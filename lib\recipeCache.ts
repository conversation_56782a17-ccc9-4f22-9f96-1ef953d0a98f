import { RecipeStep } from '@/types/calculator';

interface CacheEntry {
  recipe: RecipeStep[];
  timestamp: number;
  key: string;
}

class RecipeCache {
  private cache = new Map<string, CacheEntry>();
  private maxSize = 50; // Máximo de 50 receitas em cache
  private ttl = 5 * 60 * 1000; // 5 minutos de TTL

  private generateKey(agua: string, perfilSabor: string, perfilCorpo: string): string {
    return `${agua}-${perfilSabor}-${perfilCorpo}`;
  }

  get(agua: string, perfilSabor: string, perfilCorpo: string): RecipeStep[] | null {
    const key = this.generateKey(agua, perfilSabor, perfilCorpo);
    const entry = this.cache.get(key);

    if (!entry) {
      return null;
    }

    // Verificar se o cache expirou
    if (Date.now() - entry.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.recipe;
  }

  set(agua: string, perfilSabor: string, perfilCorpo: string, recipe: RecipeStep[]): void {
    const key = this.generateKey(agua, perfilSabor, perfilCorpo);

    // Se o cache está cheio, remover a entrada mais antiga
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }

    this.cache.set(key, {
      recipe: [...recipe], // Clonar para evitar mutações
      timestamp: Date.now(),
      key
    });
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  // Limpar entradas expiradas
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// Instância singleton do cache
export const recipeCache = new RecipeCache();

// Limpar cache expirado a cada 2 minutos
if (typeof window !== 'undefined') {
  setInterval(() => {
    recipeCache.cleanup();
  }, 2 * 60 * 1000);
}
