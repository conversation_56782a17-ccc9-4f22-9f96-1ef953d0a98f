"use client";

import { useRef } from 'react';
import { CalculatorForm } from './CalculatorForm';
import { TimerSection } from './TimerSection';
import { NavigationSidebar } from './NavigationSidebar';
import { ShareModal } from './ShareModal';
import { Header } from './Header';
import { Footer } from './Footer';
import { MainTitle } from './MainTitle';
import { StructuredDataScript } from './StructuredDataScript';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { PerformanceMonitor } from '@/components/dev/PerformanceMonitor';
import { ResourcePreloader } from '@/components/ResourcePreloader';
import { useCalculator } from '@/hooks/useCalculator';
import { useTimer } from '@/hooks/useTimer';
import { useUI } from '@/hooks/useUI';
import { useServiceWorker } from '@/hooks/useServiceWorker';

export function CalculatorPage() {
  const calculator = useCalculator();
  const ui = useUI();
  const receitaRef = useRef<HTMLDivElement | null>(null);
  // Registrar Service Worker para cache offline
  useServiceWorker();

  const timer = useTimer({
    calcularReceita: calculator.calcularReceita,
    cafe: calculator.state.cafe,
    agua: calculator.state.agua,
    proporcao: calculator.state.proporcao,
    perfilSabor: calculator.state.perfilSabor,
    perfilCorpo: calculator.state.perfilCorpo,
    receitaRef
  });

  return (
    <ErrorBoundary>
      <StructuredDataScript />
      <ResourcePreloader />

      <div className="min-h-screen flex flex-col bg-background">
        <Header onOpenSidebar={ui.abrirSidebar} />

        <NavigationSidebar
          isOpen={ui.state.sidebarAberta}
          isClosing={ui.state.sidebarFechando}
          onClose={ui.fecharSidebar}
          sidebarRef={ui.sidebarRef}
        />

        <div className="flex flex-col items-center justify-start pt-2 sm:pt-4 px-4 sm:px-8 gap-4 sm:gap-8 flex-1">
          <MainTitle />

          <div className="w-full max-w-6xl grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-8 items-start">
            <ErrorBoundary>
              <CalculatorForm
                calculator={calculator}
                inputsDesabilitados={timer.inputsDesabilitados}
              />
            </ErrorBoundary>

            <ErrorBoundary>
              <TimerSection
                timer={timer}
                calculator={calculator}
                onOpenModal={ui.abrirModal}
                receitaRef={receitaRef}
              />
            </ErrorBoundary>
          </div>
        </div>

        <Footer />

        <ShareModal
          isOpen={ui.state.modalAberto}
          onClose={ui.fecharModal}
          calculator={calculator}
        />

        <PerformanceMonitor />
      </div>
    </ErrorBoundary>
  );
}
