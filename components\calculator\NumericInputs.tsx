import { Lock, Unlock } from "lucide-react";
import { useKeyboardNavigation } from '@/hooks/useKeyboardNavigation';

interface NumericInputsProps {
  cafe: string;
  agua: string;
  proporcao: string;
  aguaBloqueada: boolean;
  proporcaoBloqueada: boolean;
  onCafeChange: (valor: string) => void;
  onAguaChange: (valor: string) => void;
  onProporcaoChange: (valor: string) => void;
  onToggleAguaBloqueada: () => void;
  onToggleProporcaoBloqueada: () => void;
  inputsDesabilitados: () => boolean;
}

export function NumericInputs({
  cafe,
  agua,
  proporcao,
  aguaBloqueada,
  proporcaoBloqueada,
  onCafeChange,
  onAguaChange,
  onProporcaoChange,
  onToggleAguaBloqueada,
  onToggleProporcaoBloqueada,
  inputsDesabilitados
}: NumericInputsProps) {
  const { handleFocus, handleKeyDown, handleWheel } = useKeyboardNavigation();
  const isDisabled = inputsDesabilitados();

  return (
    <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-4 mb-4 sm:mb-6">
      {/* Campo Café */}
      <div>
        <label htmlFor="cafe-input" className="block text-xs sm:text-sm font-medium text-foreground mb-1 sm:mb-2">
          Café (g)
        </label>
        <input
          id="cafe-input"
          name="cafe"
          type="number"
          autoComplete="off"
          value={cafe}
          onChange={(e) => !isDisabled && onCafeChange(e.target.value)}
          onFocus={handleFocus}
          onKeyDown={handleKeyDown}
          onWheel={handleWheel}
          min="0"
          step="0.1"
          disabled={isDisabled}
          aria-label="Quantidade de café em gramas"
          className={`w-full px-3 py-2.5 sm:py-2 border border-border rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none text-base ${
            isDisabled
              ? "bg-muted text-muted-foreground cursor-not-allowed"
              : "bg-background"
          }`}
        />
      </div>

      {/* Campo Água */}
      <div>
        <label htmlFor="agua-input" className="block text-xs sm:text-sm font-medium text-foreground mb-1 sm:mb-2">
          Água (ml)
        </label>
        <div className="relative">
          <input
            id="agua-input"
            name="agua"
            type="number"
            autoComplete="off"
            value={agua}
            onChange={(e) => !aguaBloqueada && !isDisabled && onAguaChange(e.target.value)}
            onFocus={handleFocus}
            onKeyDown={handleKeyDown}
            onWheel={handleWheel}
            min="0"
            step="0.1"
            disabled={aguaBloqueada || isDisabled}
            aria-label="Quantidade de água em mililitros"
            className={`w-full px-3 py-2.5 sm:py-2 pr-12 sm:pr-10 border border-border rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none text-base ${
              aguaBloqueada || isDisabled
                ? "bg-muted text-muted-foreground cursor-not-allowed"
                : "bg-background"
            }`}
          />
          <button
            type="button"
            onClick={() => !isDisabled && onToggleAguaBloqueada()}
            disabled={isDisabled}
            aria-label={aguaBloqueada ? "Desbloquear campo de água" : "Bloquear campo de água"}
            className={`absolute right-1 sm:right-2 top-1/2 transform -translate-y-1/2 p-2 sm:p-1 transition-colors min-w-[44px] min-h-[44px] sm:min-w-auto sm:min-h-auto flex items-center justify-center touch-target focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded ${
              isDisabled
                ? "text-muted-foreground cursor-not-allowed"
                : "text-muted-foreground hover:text-foreground active:text-primary"
            }`}
          >
            {aguaBloqueada ? <Lock size={20} className="sm:w-4 sm:h-4" strokeWidth={1.5} /> : <Unlock size={20} className="sm:w-4 sm:h-4" strokeWidth={1.5} />}
          </button>
        </div>
      </div>

      {/* Campo Proporção */}
      <div>
        <label htmlFor="proporcao-input" className="block text-xs sm:text-sm font-medium text-foreground mb-1 sm:mb-2">
          Proporção (1:{proporcao})
        </label>
        <div className="relative">
          <input
            id="proporcao-input"
            name="proporcao"
            type="number"
            autoComplete="off"
            value={proporcao}
            onChange={(e) => !proporcaoBloqueada && !isDisabled && onProporcaoChange(e.target.value)}
            onFocus={handleFocus}
            onKeyDown={handleKeyDown}
            onWheel={handleWheel}
            min="0"
            step="0.1"
            disabled={proporcaoBloqueada || isDisabled}
            aria-label="Proporção café para água"
            className={`w-full px-3 py-2.5 sm:py-2 pr-12 sm:pr-10 border border-border rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none text-base ${
              proporcaoBloqueada || isDisabled
                ? "bg-muted text-muted-foreground cursor-not-allowed"
                : "bg-background"
            }`}
          />
          <button
            type="button"
            onClick={() => !isDisabled && onToggleProporcaoBloqueada()}
            disabled={isDisabled}
            aria-label={proporcaoBloqueada ? "Desbloquear campo de proporção" : "Bloquear campo de proporção"}
            className={`absolute right-1 sm:right-2 top-1/2 transform -translate-y-1/2 p-2 sm:p-1 transition-colors min-w-[44px] min-h-[44px] sm:min-w-auto sm:min-h-auto flex items-center justify-center touch-target focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded ${
              isDisabled
                ? "text-muted-foreground cursor-not-allowed"
                : "text-muted-foreground hover:text-foreground active:text-primary"
            }`}
          >
            {proporcaoBloqueada ? <Lock size={20} className="sm:w-4 sm:h-4" strokeWidth={1.5} /> : <Unlock size={20} className="sm:w-4 sm:h-4" strokeWidth={1.5} />}
          </button>
        </div>
      </div>
    </div>
  );
}
