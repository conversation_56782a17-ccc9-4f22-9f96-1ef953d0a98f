import { useState, useRef, useEffect, useCallback } from 'react';
import { TimerState, RecipeStep } from '@/types/calculator';
import { DEFAULT_VALUES, RECIPE_TIMING } from '@/constants/app';
import { AUDIO_FILES, AUDIO_CONFIG } from '@/constants/audio';
import {
  trackRecipeGenerated,
  trackRecipeCompleted,
  trackRecipeTimer,
  trackButtonClick
} from '@/lib/analytics';

interface UseTimerProps {
  calcularReceita: () => RecipeStep[];
  cafe: string;
  agua: string;
  proporcao: string;
  perfilSabor: string;
  perfilCorpo: string;
}

export function useTimer({
  calcularReceita,
  cafe,
  agua,
  proporcao,
  perfilSabor,
  perfilCorpo
}: UseTimerProps) {
  const [state, setState] = useState<TimerState>({
    tempo: 0,
    rodando: false,
    contandoRegressivo: false,
    contagemRegressiva: DEFAULT_VALUES.contagemRegressiva
  });

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const regressivaRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const finishAudioRef = useRef<HTMLAudioElement | null>(null);
  const transitionAudioRef = useRef<HTMLAudioElement | null>(null);
  const passoAnteriorRef = useRef<number>(-1);

  // Função para calcular o tempo total da receita
  const calcularTempoTotal = useCallback(() => {
    const receita = calcularReceita();
    if (receita.length === 0) return 0;

    const ultimoPasso = receita[receita.length - 1];
    return ultimoPasso.tempoSegundos + RECIPE_TIMING.finalWaitTime;
  }, [calcularReceita]);

  // Função para determinar o passo atual baseado no tempo do cronômetro
  const obterPassoAtual = useCallback(() => {
    const receita = calcularReceita();
    if (!state.rodando && state.tempo === 0) return -1;

    let passoAtual = -1;
    for (let i = 0; i < receita.length; i++) {
      if (state.tempo >= receita[i].tempoSegundos) {
        passoAtual = i;
      } else {
        break;
      }
    }

    return passoAtual;
  }, [calcularReceita, state.rodando, state.tempo]);

  // Função para formatar tempo
  const formatarTempo = useCallback((segundos: number): string => {
    const mins = Math.floor(segundos / 60);
    const secs = segundos % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  // Inicializar áudios
  useEffect(() => {
    audioRef.current = new Audio(AUDIO_FILES.countdown);
    audioRef.current.preload = AUDIO_CONFIG.preload;

    finishAudioRef.current = new Audio(AUDIO_FILES.finish);
    finishAudioRef.current.preload = AUDIO_CONFIG.preload;

    transitionAudioRef.current = new Audio(AUDIO_FILES.transition);
    transitionAudioRef.current.preload = AUDIO_CONFIG.preload;
    transitionAudioRef.current.volume = AUDIO_CONFIG.transitionVolume;

    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
      if (finishAudioRef.current) {
        finishAudioRef.current.pause();
        finishAudioRef.current = null;
      }
      if (transitionAudioRef.current) {
        transitionAudioRef.current.pause();
        transitionAudioRef.current = null;
      }
    };
  }, []);

  // Timer principal
  useEffect(() => {
    if (state.rodando) {
      intervalRef.current = setInterval(() => {
        setState(prev => {
          const novoTempo = prev.tempo + 1;
          const tempoTotal = calcularTempoTotal();

          if (novoTempo >= tempoTotal) {
            const receita = calcularReceita();
            trackRecipeCompleted(tempoTotal, receita.length);
            
            if (finishAudioRef.current) {
              finishAudioRef.current.currentTime = 0;
              finishAudioRef.current.play().catch(console.error);
            }
            
            return {
              ...prev,
              tempo: tempoTotal,
              rodando: false
            };
          }

          return { ...prev, tempo: novoTempo };
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [state.rodando, calcularTempoTotal, calcularReceita]);

  // Contagem regressiva
  useEffect(() => {
    if (state.contandoRegressivo) {
      if (state.contagemRegressiva > 0) {
        regressivaRef.current = setTimeout(() => {
          setState(prev => ({
            ...prev,
            contagemRegressiva: prev.contagemRegressiva - 1
          }));
        }, 1000);
      } else {
        setState(prev => ({
          ...prev,
          contandoRegressivo: false,
          rodando: true,
          contagemRegressiva: DEFAULT_VALUES.contagemRegressiva
        }));
      }
    }

    return () => {
      if (regressivaRef.current) {
        clearTimeout(regressivaRef.current);
      }
    };
  }, [state.contandoRegressivo, state.contagemRegressiva]);

  // Detectar mudanças de passo e reproduzir áudio de transição
  useEffect(() => {
    const passoAtual = obterPassoAtual();

    if (passoAtual !== passoAnteriorRef.current && 
        passoAtual > passoAnteriorRef.current && 
        passoAtual > 0) {
      if (transitionAudioRef.current) {
        transitionAudioRef.current.currentTime = 0;
        transitionAudioRef.current.play().catch(console.error);
      }
    }

    passoAnteriorRef.current = passoAtual;
  }, [obterPassoAtual]);

  const iniciarPausar = useCallback(() => {
    if (state.rodando) {
      setState(prev => ({ ...prev, rodando: false }));
      trackRecipeTimer('pause', state.tempo);
      trackButtonClick('pausar_receita', 'calculadora');
    } else if (state.contandoRegressivo) {
      setState(prev => ({
        ...prev,
        contandoRegressivo: false,
        contagemRegressiva: DEFAULT_VALUES.contagemRegressiva
      }));
      trackRecipeTimer('reset');
      trackButtonClick('cancelar_contagem', 'calculadora');
      
      // Parar áudios
      [audioRef, finishAudioRef, transitionAudioRef].forEach(ref => {
        if (ref.current) {
          ref.current.pause();
          ref.current.currentTime = 0;
        }
      });
    } else {
      if (state.tempo > 0) {
        setState(prev => ({ ...prev, rodando: true }));
        trackRecipeTimer('resume', state.tempo);
        trackButtonClick('retomar_receita', 'calculadora');
      } else {
        setState(prev => ({ ...prev, contandoRegressivo: true }));
        trackRecipeGenerated({
          cafe: parseFloat(cafe),
          agua: parseFloat(agua),
          proporcao: parseFloat(proporcao),
          perfilSabor,
          perfilCorpo
        });
        trackRecipeTimer('start');
        trackButtonClick('iniciar_receita', 'calculadora');
        
        if (audioRef.current) {
          audioRef.current.currentTime = 0;
          audioRef.current.play().catch(console.error);
        }
      }
    }
  }, [state, cafe, agua, proporcao, perfilSabor, perfilCorpo]);

  const resetar = useCallback(() => {
    trackRecipeTimer('reset', state.tempo);
    trackButtonClick('reiniciar_receita', 'calculadora');
    
    setState({
      tempo: 0,
      rodando: false,
      contandoRegressivo: false,
      contagemRegressiva: DEFAULT_VALUES.contagemRegressiva
    });
    
    passoAnteriorRef.current = -1;
    
    // Parar áudios
    [audioRef, finishAudioRef, transitionAudioRef].forEach(ref => {
      if (ref.current) {
        ref.current.pause();
        ref.current.currentTime = 0;
      }
    });
  }, [state.tempo]);

  const inputsDesabilitados = useCallback(() => {
    return state.rodando || state.contandoRegressivo || state.tempo > 0;
  }, [state]);

  return {
    state,
    calcularTempoTotal,
    obterPassoAtual,
    formatarTempo,
    iniciarPausar,
    resetar,
    inputsDesabilitados
  };
}
