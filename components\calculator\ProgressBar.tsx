import { RecipeStep } from '@/types/calculator';

interface ProgressBarProps {
  tempo: number;
  rodando: boolean;
  contandoRegressivo: boolean;
  tempoTotal: number;
  receita: RecipeStep[];
  obterPassoAtual: () => number;
}

export function ProgressBar({
  tempo,
  rodando,
  contandoRegressivo,
  tempoTotal,
  receita,
  obterPassoAtual
}: ProgressBarProps) {
  if (!(rodando || tempo > 0) || contandoRegressivo) {
    return null;
  }

  const passoAtual = obterPassoAtual();

  return (
    <div className="w-full mt-4" role="progressbar" aria-valuenow={tempo} aria-valuemax={tempoTotal} aria-label="Progresso da receita">
      <div className="relative">
        {/* Linha de fundo */}
        <div className="w-full h-1 bg-secondary rounded-full"></div>

        {/* Linha de progresso */}
        <div
          className="absolute top-0 h-1 bg-primary rounded-full transition-all duration-1000 ease-out"
          style={{
            width: `${Math.min((tempo / tempoTotal) * 100, 100)}%`
          }}
        />

        {/* Marcadores dos passos */}
        {receita.map((passo, index) => {
          const posicao = (passo.tempoSegundos / tempoTotal) * 100;
          const isConcluido = index <= passoAtual;
          const isAtivo = index === passoAtual;

          return (
            <div
              key={index}
              className="absolute top-1/2 transform -translate-x-1/2 -translate-y-1/2"
              style={{ left: `${posicao}%` }}
              aria-label={`Passo ${index + 1}: ${isConcluido ? 'concluído' : isAtivo ? 'ativo' : 'pendente'}`}
            >
              <div
                className={`w-0.5 h-2 transition-all duration-500 ${
                  isAtivo
                    ? 'bg-primary scale-110 shadow-md'
                    : isConcluido
                    ? 'bg-primary'
                    : 'bg-muted-foreground/60'
                }`}
              />
            </div>
          );
        })}

        {/* Marcador final */}
        <div
          className="absolute top-1/2 transform -translate-x-1/2 -translate-y-1/2"
          style={{ left: '100%' }}
          aria-label={`Finalização: ${tempo >= tempoTotal ? 'concluída' : 'pendente'}`}
        >
          <div
            className={`w-0.5 h-2 transition-all duration-500 ${
              tempo >= tempoTotal
                ? 'bg-primary'
                : 'bg-muted-foreground/60'
            }`}
          />
        </div>
      </div>
    </div>
  );
}
