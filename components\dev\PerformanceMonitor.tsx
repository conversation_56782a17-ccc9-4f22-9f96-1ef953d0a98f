"use client";

import { useState, useEffect } from 'react';
import { useAdvancedPreferences } from '@/hooks/useAdvancedPreferences';

interface PerformanceMetrics {
  fps: number;
  memoryUsage: number;
  renderTime: number;
  jsHeapSize: number;
}

export function PerformanceMonitor() {
  const { preferences } = useAdvancedPreferences();
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 0,
    memoryUsage: 0,
    renderTime: 0,
    jsHeapSize: 0
  });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (!preferences.debugMode) return;

    let frameCount = 0;
    let lastTime = performance.now();
    let animationId: number;

    const measurePerformance = () => {
      const now = performance.now();
      frameCount++;

      if (now - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (now - lastTime));
        
        const memory = (performance as { memory?: { usedJSHeapSize: number; totalJSHeapSize: number } }).memory;
        const memoryUsage = memory ? Math.round(memory.usedJSHeapSize / 1024 / 1024) : 0;
        const jsHeapSize = memory ? Math.round(memory.totalJSHeapSize / 1024 / 1024) : 0;

        setMetrics(prev => ({
          ...prev,
          fps,
          memoryUsage,
          jsHeapSize,
          renderTime: now - lastTime
        }));

        frameCount = 0;
        lastTime = now;
      }

      animationId = requestAnimationFrame(measurePerformance);
    };

    measurePerformance();

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [preferences.debugMode]);

  if (!preferences.debugMode) return null;

  return (
    <>
      {/* Botão toggle */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 z-50 bg-primary text-primary-foreground p-2 rounded-full shadow-lg hover:bg-primary/90 transition-colors"
        title="Performance Monitor"
      >
        📊
      </button>

      {/* Monitor */}
      {isVisible && (
        <div className="fixed bottom-16 right-4 z-50 bg-card border border-border rounded-lg shadow-xl p-4 min-w-[200px]">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold text-foreground">Performance</h3>
            <button
              onClick={() => setIsVisible(false)}
              className="text-muted-foreground hover:text-foreground"
            >
              ✕
            </button>
          </div>
          
          <div className="space-y-2 text-xs">
            <div className="flex justify-between">
              <span className="text-muted-foreground">FPS:</span>
              <span className={`font-mono ${metrics.fps < 30 ? 'text-red-500' : metrics.fps < 50 ? 'text-yellow-500' : 'text-green-500'}`}>
                {metrics.fps}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-muted-foreground">Memory:</span>
              <span className={`font-mono ${metrics.memoryUsage > 100 ? 'text-red-500' : metrics.memoryUsage > 50 ? 'text-yellow-500' : 'text-green-500'}`}>
                {metrics.memoryUsage}MB
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-muted-foreground">Heap:</span>
              <span className="font-mono text-foreground">
                {metrics.jsHeapSize}MB
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-muted-foreground">Render:</span>
              <span className={`font-mono ${metrics.renderTime > 16 ? 'text-red-500' : 'text-green-500'}`}>
                {metrics.renderTime.toFixed(1)}ms
              </span>
            </div>
          </div>

          {/* Alertas de performance */}
          <div className="mt-3 pt-3 border-t border-border">
            {metrics.fps < 30 && (
              <div className="text-xs text-red-500 mb-1">⚠️ Low FPS detected</div>
            )}
            {metrics.memoryUsage > 100 && (
              <div className="text-xs text-red-500 mb-1">⚠️ High memory usage</div>
            )}
            {metrics.renderTime > 16 && (
              <div className="text-xs text-yellow-500 mb-1">⚠️ Slow render detected</div>
            )}
          </div>
        </div>
      )}
    </>
  );
}
